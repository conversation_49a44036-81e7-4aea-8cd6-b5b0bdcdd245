<template>
  <div class="friend-list">
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="el-icon-user-solid"></i>
            好友管理
          </h1>
          <p class="page-subtitle">与朋友一起进步，让竞争更有意义</p>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ friendList.length }}</div>
            <div class="stat-label">好友</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ requestList.length }}</div>
            <div class="stat-label">待处理</div>
          </div>
        </div>
      </div>
    </div>

    <div class="tabs-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange" class="custom-tabs">
        <div class="tabs-header-decoration"></div>
        <el-tab-pane name="friends">
          <span slot="label" class="tab-label">
            <i class="el-icon-user"></i>
            我的好友
            <el-badge v-if="friendList.length > 0" :value="friendList.length" class="friend-badge" type="primary"></el-badge>
          </span>
          <div class="friend-list-container">
            <div class="friend-list-header" v-if="friendList.length > 0">
              <div class="friend-filters">
                <div class="search-filter">
                  <el-input
                    v-model="friendSearchKeyword"
                    placeholder="搜索好友..."
                    prefix-icon="el-icon-search"
                    clearable
                    @input="filterFriends"
                    class="friend-search-input"
                  >
                  </el-input>
                </div>
                <div class="group-filter">
                  <el-select v-model="selectedGroup" placeholder="全部分组" @change="filterFriends" class="group-select">
                    <el-option label="全部好友" value="all"></el-option>
                    <el-option label="在线好友" value="online"></el-option>
                    <el-option label="常用联系人" value="frequent"></el-option>
                    <el-option label="竞争伙伴" value="competition"></el-option>
                  </el-select>
                </div>
                <div class="sort-filter">
                  <el-select v-model="sortOption" placeholder="排序方式" @change="sortFriends" class="sort-select">
                    <el-option label="默认排序" value="default"></el-option>
                    <el-option label="按活跃度" value="activity"></el-option>
                    <el-option label="按积分" value="points"></el-option>
                    <el-option label="按名称" value="name"></el-option>
                  </el-select>
                </div>
              </div>
              <div class="view-options">
                <el-radio-group v-model="viewMode" size="small" @change="changeViewMode" class="view-mode-switch">
                  <el-radio-button label="grid">
                    <i class="el-icon-s-grid"></i>
                  </el-radio-button>
                  <el-radio-button label="list">
                    <i class="el-icon-menu"></i>
                  </el-radio-button>
                </el-radio-group>
              </div>
            </div>

            <div v-if="friendList.length === 0" class="empty-friends">
              <div class="empty-friends-illustration">
                <img src="https://cdn.jsdelivr.net/gh/bytedance/IconPark/source/svg/people.svg" alt="空好友列表" class="empty-image" />
                <div class="empty-decoration"></div>
              </div>
              <h3 class="empty-title">还没有好友</h3>
              <p class="empty-description">添加好友一起竞争，让进步更有动力</p>
              <div class="empty-actions">
                <el-button type="primary" @click="activeTab = 'add'" class="add-friend-action">
                  <i class="el-icon-plus"></i>
                  添加好友
                </el-button>
                <el-button type="text" @click="activeTab = 'requests'" class="check-requests-action">
                  <i class="el-icon-bell"></i>
                  查看好友请求
                  <span v-if="requestList.length > 0" class="request-count-badge">{{ requestList.length }}</span>
                </el-button>
              </div>
              <div class="empty-features">
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="el-icon-trophy"></i>
                  </div>
                  <div class="feature-text">
                    <h4>共同竞争</h4>
                    <p>与好友一起参与竞争，互相激励</p>
                  </div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="el-icon-data-line"></i>
                  </div>
                  <div class="feature-text">
                    <h4>进度追踪</h4>
                    <p>实时查看好友进度，保持动力</p>
                  </div>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">
                    <i class="el-icon-chat-dot-round"></i>
                  </div>
                  <div class="feature-text">
                    <h4>即时互动</h4>
                    <p>与好友交流心得，共同成长</p>
                  </div>
                </div>
              </div>
            </div>

            <div v-else-if="filteredFriendList.length === 0" class="no-search-results">
              <div class="no-results-icon">
                <i class="el-icon-search"></i>
              </div>
              <h3 class="no-results-title">未找到匹配的好友</h3>
              <p class="no-results-description">尝试使用其他关键词或清除筛选条件</p>
              <el-button type="primary" @click="resetFilters" class="reset-filters-btn">
                <i class="el-icon-refresh"></i>
                清除筛选
              </el-button>
            </div>

            <div v-else :class="['friend-grid', viewMode === 'list' ? 'list-view' : 'grid-view']">
              <div
                v-for="friend in filteredFriendList"
                :key="friend.id"
                class="friend-card"
                :class="{'list-mode': viewMode === 'list'}"
                @click="viewFriendProfile(friend)"
              >
                <div class="friend-card-header">
                  <div class="friend-status-indicator" :class="getStatusClass(friend)"></div>
                  <div class="friend-level-badge" v-if="getFriendLevel(friend)">
                    <i class="el-icon-star-on"></i>
                    {{ getFriendLevel(friend) }}
                  </div>
                </div>

                <div class="friend-card-content">
                  <div class="friend-avatar-section">
                    <div class="avatar-container">
                      <el-avatar :src="friend.avatar" :size="64" class="friend-avatar">
                        {{ friend.nickname ? friend.nickname.charAt(0) : friend.username.charAt(0) }}
                      </el-avatar>
                      <div class="avatar-ring" :class="{ active: friend.isOnline }"></div>
                    </div>
                    <div class="online-status" :class="{ online: friend.isOnline }">
                      <span class="status-text">{{ friend.isOnline ? '在线' : '离线' }}</span>
                    </div>
                  </div>

                  <div class="friend-info">
                    <div class="friend-header">
                      <h4 class="friend-name">{{ friend.nickname || friend.username }}</h4>
                      <div class="friend-badges">
                        <span v-if="friend.isVip" class="vip-badge">
                          <i class="el-icon-crown"></i>
                          VIP
                        </span>
                      </div>
                    </div>

                    <div class="friend-meta">
                      <div class="meta-item">
                        <i class="el-icon-trophy"></i>
                        <span class="meta-label">积分</span>
                        <span class="meta-value">{{ formatPoints(friend.totalPoints || 0) }}</span>
                      </div>
                      <div class="meta-item">
                        <i class="el-icon-time"></i>
                        <span class="meta-label">活跃</span>
                        <span class="meta-value">{{ formatLastSeen(friend.lastLoginTime) }}</span>
                      </div>
                      <div v-if="friend.competitionCount" class="meta-item">
                        <i class="el-icon-medal"></i>
                        <span class="meta-label">竞争</span>
                        <span class="meta-value">{{ friend.competitionCount }}次</span>
                      </div>
                    </div>

                    <div v-if="friend.remark" class="friend-remark">
                      <i class="el-icon-chat-line-square"></i>
                      <span>{{ friend.remark }}</span>
                    </div>
                  </div>
                </div>

                <div class="friend-card-footer" @click.stop>
                  <div class="friend-actions">
                    <el-button
                      type="primary"
                      size="small"
                      @click="inviteCompetition(friend)"
                      class="action-btn primary-btn"
                    >
                      <i class="el-icon-trophy"></i>
                      邀请竞争
                    </el-button>
                    <el-dropdown @command="handleFriendAction" trigger="click" class="more-dropdown">
                      <el-button size="small" class="action-btn more-btn">
                        <i class="el-icon-more"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown" class="friend-dropdown">
                        <el-dropdown-item :command="{action: 'remark', friend}">
                          <i class="el-icon-edit"></i>
                          修改备注
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'profile', friend}">
                          <i class="el-icon-view"></i>
                          查看资料
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'message', friend}">
                          <i class="el-icon-chat-dot-round"></i>
                          发送消息
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'delete', friend}" divided>
                          <i class="el-icon-delete"></i>
                          删除好友
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane name="requests">
          <span slot="label" class="tab-label">
            <i class="el-icon-bell"></i>
            好友请求
            <el-badge v-if="requestList.length > 0" :value="requestList.length" class="request-badge" type="danger"></el-badge>
          </span>
          <div class="request-section">
            <div v-if="loading" class="loading-container">
              <el-loading-spinner></el-loading-spinner>
              <p>加载中...</p>
            </div>

            <div v-else-if="requestList.length === 0" class="empty-requests">
              <div class="empty-icon">
                <i class="el-icon-bell"></i>
              </div>
              <h3 class="empty-title">暂无好友请求</h3>
              <p class="empty-description">当有人向你发送好友请求时，会在这里显示</p>
              <div class="empty-actions">
                <el-button type="primary" @click="activeTab = 'add'" class="empty-action">
                  <i class="el-icon-plus"></i>
                  主动添加好友
                </el-button>
                <el-button type="text" @click="refreshRequests" class="refresh-action">
                  <i class="el-icon-refresh"></i>
                  刷新列表
                </el-button>
              </div>
            </div>

            <div v-else class="request-list">
              <div class="request-header">
                <h3 class="request-title">
                  <i class="el-icon-message"></i>
                  收到的好友请求
                  <span class="request-count">({{ requestList.length }})</span>
                </h3>
                <div class="request-actions">
                  <el-button
                    size="mini"
                    type="text"
                    @click="refreshRequests"
                    class="refresh-action"
                  >
                    <i class="el-icon-refresh"></i>
                    刷新
                  </el-button>
                  <el-button
                    v-if="requestList.length > 1"
                    size="mini"
                    type="text"
                    @click="handleBatchAccept"
                    class="batch-action"
                  >
                    <i class="el-icon-check"></i>
                    全部接受
                  </el-button>
                </div>
              </div>

              <div class="request-grid">
                <div
                  v-for="request in requestList"
                  :key="request.friendshipId || request.id"
                  class="request-card"
                  :class="{ 'processing': request.accepting || request.rejecting }"
                >
                  <div class="request-card-header">
                    <div class="request-avatar-section">
                      <el-avatar :src="request.avatar" :size="56" class="request-avatar">
                        {{ request.nickname ? request.nickname.charAt(0) : request.username.charAt(0) }}
                      </el-avatar>
                      <div class="request-badge">
                        <i class="el-icon-plus"></i>
                      </div>
                    </div>
                    <div class="request-info">
                      <h4 class="request-name">{{ request.nickname || request.username }}</h4>
                      <p class="request-time">
                        <i class="el-icon-time"></i>
                        {{ formatDate(request.requestTime) }}
                      </p>
                      <p v-if="request.email" class="request-email">
                        <i class="el-icon-message"></i>
                        {{ request.email }}
                      </p>
                    </div>
                  </div>

                  <div v-if="request.remark" class="request-message">
                    <div class="message-content">
                      <i class="el-icon-chat-line-square"></i>
                      <span>{{ request.remark }}</span>
                    </div>
                  </div>

                  <div class="request-actions">
                    <el-button
                      type="success"
                      size="small"
                      @click="acceptRequest(request)"
                      :loading="request.accepting"
                      class="accept-btn"
                    >
                      <i class="el-icon-check"></i>
                      接受
                    </el-button>
                    <el-button
                      plain
                      type="danger"
                      size="small"
                      @click="rejectRequest(request)"
                      :loading="request.rejecting"
                      class="reject-btn"
                    >
                      <i class="el-icon-close"></i>
                      拒绝
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane name="add">
          <span slot="label" class="tab-label">
            <i class="el-icon-plus"></i>
            添加好友
            <span class="tab-highlight"></span>
          </span>
        <div class="add-friend-section">
          <div class="search-container">
            <div class="search-header">
              <div class="search-title-section">
                <h3 class="search-title">
                  <i class="el-icon-search"></i>
                  发现新朋友
                </h3>
                <p class="search-description">输入用户名或昵称，找到志同道合的伙伴</p>
              </div>
              <div class="search-stats" v-if="searchResults.length > 0">
                <div class="stat-item">
                  <span class="stat-number">{{ searchResults.length }}</span>
                  <span class="stat-label">找到用户</span>
                </div>
              </div>
            </div>

            <div class="search-box">
              <div class="search-input-wrapper">
                <el-input
                  v-model="searchKeyword"
                  placeholder="输入用户名、昵称或邮箱..."
                  @keyup.enter.native="searchUsers"
                  @input="handleSearchInput"
                  size="large"
                  class="search-input"
                  clearable
                >
                  <i slot="prefix" class="el-icon-search search-icon"></i>
                </el-input>
                <el-button
                  @click="searchUsers"
                  :loading="searchLoading"
                  type="primary"
                  size="large"
                  class="search-btn"
                >
                  <i class="el-icon-search"></i>
                  搜索
                </el-button>
              </div>

              <div class="search-suggestions" v-if="!searchKeyword && !searchLoading">
                <div class="suggestion-item" @click="searchKeyword = '新手'; searchUsers()">
                  <i class="el-icon-user"></i>
                  <span>寻找新手用户</span>
                </div>
                <div class="suggestion-item" @click="searchKeyword = '高手'; searchUsers()">
                  <i class="el-icon-trophy"></i>
                  <span>寻找高手用户</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="searchLoading" class="search-loading">
            <div class="loading-animation">
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <p class="loading-text">正在搜索用户...</p>
          </div>

          <div v-else-if="searchResults.length > 0" class="search-results">
            <div class="results-header">
              <h3 class="results-title">
                <i class="el-icon-user-solid"></i>
                搜索结果
                <span class="results-count">({{ searchResults.length }})</span>
              </h3>
              <div class="results-actions">
                <el-button
                  size="mini"
                  type="text"
                  @click="clearSearch"
                  class="clear-btn"
                >
                  <i class="el-icon-refresh-left"></i>
                  重新搜索
                </el-button>
              </div>
            </div>

            <div class="results-grid">
              <div
                v-for="user in searchResults"
                :key="user.id"
                class="user-card"
                :class="{
                  'is-friend': user.isFriend,
                  'request-sent': user.requestSent,
                  'high-score': user.totalPoints > 1000
                }"
              >
                <div class="user-card-header">
                  <div class="user-level-indicator" v-if="getUserLevel(user)">
                    <i class="el-icon-star-on"></i>
                    {{ getUserLevel(user) }}
                  </div>
                  <div class="user-status-badge" v-if="user.isFriend">
                    <i class="el-icon-check"></i>
                    好友
                  </div>
                  <div class="user-status-badge pending" v-else-if="user.requestSent">
                    <i class="el-icon-time"></i>
                    已发送
                  </div>
                </div>

                <div class="user-card-content">
                  <div class="user-avatar-section">
                    <el-avatar :src="user.avatar" :size="64" class="user-avatar">
                      {{ user.nickname ? user.nickname.charAt(0) : user.username.charAt(0) }}
                    </el-avatar>
                    <div class="avatar-decoration"></div>
                  </div>

                  <div class="user-info">
                    <h4 class="user-name">{{ user.nickname || user.username }}</h4>
                    <div class="user-meta">
                      <div class="meta-item">
                        <i class="el-icon-trophy"></i>
                        <span>{{ formatPoints(user.totalPoints || 0) }} 积分</span>
                      </div>
                      <div v-if="user.email" class="meta-item">
                        <i class="el-icon-message"></i>
                        <span>{{ user.email }}</span>
                      </div>
                      <div v-if="user.location" class="meta-item">
                        <i class="el-icon-location"></i>
                        <span>{{ user.location }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="user-card-footer">
                  <el-button
                    :type="getButtonType(user)"
                    size="small"
                    @click="sendFriendRequest(user)"
                    :disabled="user.isFriend || user.requestSent"
                    :loading="user.requesting"
                    class="add-friend-btn"
                  >
                    <i :class="getButtonIcon(user)"></i>
                    {{ getButtonText(user) }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="searchKeyword && !searchLoading" class="no-results">
            <div class="no-results-icon">
              <i class="el-icon-search"></i>
            </div>
            <h3 class="no-results-title">未找到相关用户</h3>
            <p class="no-results-description">
              没有找到与 "<strong>{{ searchKeyword }}</strong>" 相关的用户
            </p>
            <div class="no-results-suggestions">
              <p class="suggestions-title">建议：</p>
              <ul class="suggestions-list">
                <li>检查拼写是否正确</li>
                <li>尝试使用更简短的关键词</li>
                <li>使用用户的昵称或邮箱搜索</li>
              </ul>
            </div>
            <el-button type="primary" @click="clearSearch" class="retry-btn">
              <i class="el-icon-refresh"></i>
              重新搜索
            </el-button>
          </div>

          <div v-else class="search-placeholder">
            <div class="placeholder-content">
              <div class="placeholder-icon">
                <i class="el-icon-user-solid"></i>
              </div>
              <h3 class="placeholder-title">开始寻找好友</h3>
              <p class="placeholder-description">
                输入用户名、昵称或邮箱地址，<br>
                找到志同道合的竞争伙伴
              </p>
              <div class="placeholder-features">
                <div class="feature-item">
                  <i class="el-icon-search"></i>
                  <span>精准搜索</span>
                </div>
                <div class="feature-item">
                  <i class="el-icon-user"></i>
                  <span>实时匹配</span>
                </div>
                <div class="feature-item">
                  <i class="el-icon-trophy"></i>
                  <span>共同进步</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态 -->
    <div v-if="isEmpty && !loading" class="empty-state">
      <i class="el-icon-user"></i>
      <p>{{ getEmptyText() }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FriendList',

  data() {
    return {
      loading: false,
      searchLoading: false,
      activeTab: 'friends',
      friendList: [],
      filteredFriendList: [],
      friendSearchKeyword: '',
      selectedGroup: 'all',
      sortOption: 'default',
      viewMode: 'grid',
      requestList: [],
      searchResults: [],
      searchKeyword: '',
      searchTimer: null
    }
  },

  computed: {
    isEmpty() {
      switch (this.activeTab) {
        case 'friends': return this.friendList.length === 0
        case 'requests': return this.requestList.length === 0
        case 'add': return this.searchResults.length === 0 && this.searchKeyword
        default: return false
      }
    }
  },

  async created() {
    // 检查URL参数，如果有tab参数则切换到对应标签页
    const tab = this.$route.query.tab
    if (tab && ['friends', 'requests', 'add'].includes(tab)) {
      this.activeTab = tab
    }

    // 首屏同时加载好友列表与收到的请求，以便对方能立即看到待处理数量
    try {
      await Promise.all([
        this.loadFriends(),
        this.loadRequests()
      ])
    } catch (e) {
      // 忽略首屏加载错误，避免阻塞页面
      console.error(e)
    }
  },

  methods: {
    async loadData() {
      this.loading = true
      try {
        if (this.activeTab === 'friends') {
          await this.loadFriends()
        } else if (this.activeTab === 'requests') {
          await this.loadRequests()
        }
      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async loadFriends() {
      const response = await this.$api.friend.getFriendList()
      this.friendList = response.data
      this.filterFriends()
    },

    filterFriends() {
      let result = [...this.friendList];

      // 按关键词过滤
      if (this.friendSearchKeyword) {
        const keyword = this.friendSearchKeyword.toLowerCase();
        result = result.filter(friend => {
          const name = (friend.nickname || friend.username || '').toLowerCase();
          const email = (friend.email || '').toLowerCase();
          const remark = (friend.remark || '').toLowerCase();
          return name.includes(keyword) || email.includes(keyword) || remark.includes(keyword);
        });
      }

      // 按分组过滤
      if (this.selectedGroup !== 'all') {
        switch (this.selectedGroup) {
          case 'online':
            result = result.filter(friend => friend.isOnline);
            break;
          case 'frequent':
            // 假设有一个常用联系人标记，这里可以根据实际情况调整
            result = result.filter(friend => friend.isFrequent || friend.contactFrequency > 5);
            break;
          case 'competition':
            // 竞争伙伴，有竞争记录的好友
            result = result.filter(friend => friend.competitionCount > 0);
            break;
        }
      }

      // 应用排序
      this.sortFriends(result);
    },

    sortFriends(friendsToSort = null) {
      let result = friendsToSort || this.filteredFriendList;

      switch (this.sortOption) {
        case 'activity':
          // 按最近活跃时间排序
          result.sort((a, b) => {
            const timeA = a.lastLoginTime ? new Date(a.lastLoginTime).getTime() : 0;
            const timeB = b.lastLoginTime ? new Date(b.lastLoginTime).getTime() : 0;
            return timeB - timeA; // 降序，最近活跃的排在前面
          });
          break;
        case 'points':
          // 按积分排序
          result.sort((a, b) => (b.totalPoints || 0) - (a.totalPoints || 0));
          break;
        case 'name':
          // 按名称排序
          result.sort((a, b) => {
            const nameA = (a.nickname || a.username || '').toLowerCase();
            const nameB = (b.nickname || b.username || '').toLowerCase();
            return nameA.localeCompare(nameB);
          });
          break;
        default:
          // 默认排序，在线的排在前面，然后按最近活跃时间
          result.sort((a, b) => {
            if (a.isOnline !== b.isOnline) {
              return a.isOnline ? -1 : 1;
            }
            const timeA = a.lastLoginTime ? new Date(a.lastLoginTime).getTime() : 0;
            const timeB = b.lastLoginTime ? new Date(b.lastLoginTime).getTime() : 0;
            return timeB - timeA;
          });
      }

      this.filteredFriendList = result;
    },

    resetFilters() {
      this.friendSearchKeyword = '';
      this.selectedGroup = 'all';
      this.sortOption = 'default';
      this.filterFriends();
    },

    changeViewMode(mode) {
      this.viewMode = mode;
    },

    async loadRequests() {
      const response = await this.$api.friend.getFriendRequests()
      this.requestList = response.data
    },

    async searchUsers() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        return
      }

      this.searchLoading = true
      try {
        const response = await this.$api.user.searchUsers(this.searchKeyword, 20)
        this.searchResults = response.data || []

        // 检查每个用户的好友状态
        for (let user of this.searchResults) {
          try {
            const friendStatus = await this.$api.friend.isFriend(user.id)
            user.isFriend = friendStatus.data

            // 检查是否已发送好友请求
            const requestStatus = await this.$api.friend.checkFriendRequestStatus(user.id)
            user.requestSent = requestStatus.data === 0 // 0表示待确认状态
          } catch (error) {
            user.isFriend = false
            user.requestSent = false
          }
        }
      } catch (error) {
        this.$message.error(error.message || '搜索失败')
        console.error(error)
        this.searchResults = []
      } finally {
        this.searchLoading = false
      }
    },

    async sendFriendRequest(user) {
      try {
        user.requesting = true
        const requestData = {
          friendId: user.id,
          remark: `来自${this.$store.getters.userInfo?.nickname || this.$store.getters.userInfo?.username || '用户'}的好友请求`
        }
        await this.$api.friend.sendFriendRequest(requestData)
        this.$message.success(`好友请求已发送给 ${user.nickname || user.username}`)

        // 更新用户状态
        user.requestSent = true
        user.requesting = false
      } catch (error) {
        this.$message.error(error.message || '发送请求失败')
        console.error(error)
        user.requesting = false
      }
    },

    getUserLevel(user) {
      const points = user.totalPoints || 0
      if (points >= 10000) return '大师'
      if (points >= 5000) return '专家'
      if (points >= 1000) return '高手'
      if (points >= 100) return '新手'
      return null
    },

    async acceptRequest(request) {
      try {
        const friendshipId = request.friendshipId || request.id
        await this.$api.friend.acceptFriendRequest(friendshipId)
        this.$message.success(`已接受 ${request.nickname || request.username} 的好友请求`)
        // 重新加载好友请求列表和好友列表
        await this.loadRequests()
        // 无论当前在哪个标签页，都要刷新好友列表，确保双方都能看到好友关系
        await this.loadFriends()
      } catch (error) {
        this.$message.error(error.message || '操作失败')
        console.error(error)
      }
    },

    async rejectRequest(request) {
      try {
        await this.$confirm(`确定要拒绝 ${request.nickname || request.username} 的好友请求吗？`, '确认拒绝', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        request.rejecting = true
        const friendshipId = request.friendshipId || request.id
        await this.$api.friend.rejectFriendRequest(friendshipId)
        this.$message.success(`已拒绝 ${request.nickname || request.username} 的好友请求`)
        this.loadRequests()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '操作失败')
          console.error(error)
        }
      } finally {
        request.rejecting = false
      }
    },

    async handleBatchAccept() {
      try {
        await this.$confirm(`确定要接受所有 ${this.requestList.length} 个好友请求吗？`, '批量接受', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        const promises = this.requestList.map(request => {
          request.accepting = true
          const friendshipId = request.friendshipId || request.id
          return this.$api.friend.acceptFriendRequest(friendshipId)
        })

        await Promise.all(promises)
        this.$message.success(`已接受所有好友请求`)
        await this.loadRequests()
        // 无论当前在哪个标签页，都要刷新好友列表，确保双方都能看到好友关系
        await this.loadFriends()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '批量操作失败')
          console.error(error)
        }
      } finally {
        this.requestList.forEach(request => {
          request.accepting = false
        })
      }
    },

    inviteCompetition(friend) {
      this.$router.push(`/competition/create?friendId=${friend.id}`)
    },

    async handleFriendAction(command) {
      const { action, friend } = command

      if (action === 'remark') {
        this.showRemarkDialog(friend)
      } else if (action === 'delete') {
        this.deleteFriend(friend)
      } else if (action === 'profile') {
        this.viewFriendProfile(friend)
      } else if (action === 'message') {
        this.sendMessage(friend)
      }
    },

    sendMessage(friend) {
      // 这里可以跳转到聊天界面或打开聊天弹窗
      this.$message.info(`即将与 ${friend.nickname || friend.username} 开始聊天`)
      // 实际实现中可以跳转到聊天页面
      // this.$router.push(`/chat/${friend.id}`)
    },

    viewFriendProfile(friend) {
      // 跳转到用户资料页面或显示用户信息弹窗
      this.$router.push(`/user/${friend.id}`)
    },

    handleSearchInput() {
      // 防抖搜索
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword.trim()) {
          this.searchUsers()
        } else {
          this.searchResults = []
        }
      }, 500)
    },

    clearSearch() {
      this.searchKeyword = ''
      this.searchResults = []
    },

    formatLastSeen(lastLoginTime) {
      if (!lastLoginTime) return '从未登录'

      const now = new Date()
      const lastLogin = new Date(lastLoginTime)
      const diffMs = now - lastLogin
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

      if (diffDays === 0) return '今天活跃'
      if (diffDays === 1) return '昨天活跃'
      if (diffDays < 7) return `${diffDays}天前活跃`
      if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前活跃`
      return '很久未登录'
    },

    getButtonType(user) {
      if (user.isFriend) return 'success'
      if (user.requestSent) return 'info'
      return 'primary'
    },

    getButtonIcon(user) {
      if (user.isFriend) return 'el-icon-check'
      if (user.requestSent) return 'el-icon-time'
      return 'el-icon-plus'
    },

    getButtonText(user) {
      if (user.isFriend) return '已是好友'
      if (user.requestSent) return '已发送'
      return '添加好友'
    },

    getStatusClass(friend) {
      if (friend.isOnline) return 'online'
      const lastLogin = new Date(friend.lastLoginTime)
      const now = new Date()
      const diffDays = Math.floor((now - lastLogin) / (1000 * 60 * 60 * 24))

      if (diffDays <= 1) return 'recent'
      if (diffDays <= 7) return 'week'
      return 'inactive'
    },

    getFriendLevel(friend) {
      const points = friend.totalPoints || 0
      if (points >= 10000) return '大师'
      if (points >= 5000) return '专家'
      if (points >= 1000) return '高手'
      if (points >= 100) return '新手'
      return null
    },

    formatPoints(points) {
      if (points >= 10000) {
        return (points / 10000).toFixed(1) + 'w'
      }
      if (points >= 1000) {
        return (points / 1000).toFixed(1) + 'k'
      }
      return points.toString()
    },

    async showRemarkDialog(friend) {
      try {
        const { value } = await this.$prompt('请输入备注名称', '修改备注', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: friend.remark || '',
          inputPlaceholder: '输入备注名称'
        })

        await this.$api.friend.updateFriendRemark(friend.id, value)
        this.$message.success('备注修改成功')
        this.loadFriends()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '修改备注失败')
          console.error(error)
        }
      }
    },

    async deleteFriend(friend) {
      try {
        await this.$confirm(`确定要删除好友 ${friend.nickname || friend.username} 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await this.$api.friend.deleteFriend(friend.id)
        this.$message.success(`已删除好友 ${friend.nickname || friend.username}`)
        this.loadFriends()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除好友失败')
          console.error(error)
        }
      }
    },

    handleTabChange() {
      this.loadData()
    },

    formatDate(date) {
      return this.$utils.formatDate(date, 'MM-DD HH:mm')
    },

    getEmptyText() {
      switch (this.activeTab) {
        case 'friends': return '还没有好友，快去添加吧'
        case 'requests': return '暂无好友请求'
        case 'add': return this.searchKeyword ? '未找到相关用户' : '输入关键词搜索用户'
        default: return '暂无数据'
      }
    },

    // 刷新好友请求列表
    async refreshRequests() {
      try {
        await this.loadRequests()
        this.$message.success('好友请求列表已刷新')
      } catch (error) {
        this.$message.error('刷新失败，请稍后重试')
        console.error('刷新好友请求失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.friend-list {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  padding: 0;

  .page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 28px 40px;
    margin-bottom: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #5a67d8, #805ad5);
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: rgba(0, 0, 0, 0.05);
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 2;
    }

    .title-section {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        i {
          color: #5a67d8;
          font-size: 24px;
          background: linear-gradient(90deg, #5a67d8, #805ad5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .page-subtitle {
        font-size: 15px;
        color: #718096;
        margin: 0;
        font-weight: 400;
      }
    }

    .header-stats {
      display: flex;
      gap: 32px;

      .stat-item {
        text-align: center;
        background: rgba(255, 255, 255, 0.7);
        padding: 10px 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
        border: 1px solid rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          background: linear-gradient(90deg, #5a67d8, #805ad5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          line-height: 1.2;
        }

        .stat-label {
          font-size: 13px;
          color: #718096;
          margin-top: 2px;
          font-weight: 500;
        }
      }
    }
  }

  .tabs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 40px 50px;

    .custom-tabs {
      background: white;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      border: 1px solid rgba(0, 0, 0, 0.03);
      position: relative;

      .tabs-header-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        z-index: 10;
      }

      ::v-deep .el-tabs__header {
        margin: 0;
        background: #f8fafc;
        border-bottom: 1px solid rgba(0, 0, 0, 0.04);
        position: relative;

        .el-tabs__nav-wrap {
          padding: 0 24px;

          &::after {
            height: 1px;
            background-color: transparent;
          }
        }

        .el-tabs__nav {
          padding: 10px 0;
        }

        .el-tabs__active-bar {
          display: none;
        }

        .el-tabs__item {
          padding: 14px 24px;
          font-size: 15px;
          font-weight: 500;
          color: #64748b;
          border: none;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          border-radius: 10px;
          margin: 0 6px;
          height: auto;
          line-height: 1.5;

          &.is-active {
            color: #5a67d8;
            background: rgba(90, 103, 216, 0.08);
            border-bottom: none;
            font-weight: 600;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(90, 103, 216, 0.1);

            .tab-label i {
              color: #5a67d8;
              transform: scale(1.2);
            }

            .tab-highlight {
              opacity: 1;
              width: 20px;
            }
          }

          &:hover {
            color: #5a67d8;
            background: rgba(90, 103, 216, 0.04);
            transform: translateY(-1px);
          }

          .tab-label {
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;

            i {
              font-size: 18px;
              transition: all 0.3s ease;
            }

            .tab-highlight {
              position: absolute;
              bottom: -14px;
              left: 50%;
              transform: translateX(-50%);
              width: 4px;
              height: 4px;
              border-radius: 2px;
              background: #5a67d8;
              opacity: 0;
              transition: all 0.3s ease;
            }
          }
        }
      }

      ::v-deep .el-tabs__content {
        padding: 28px 24px;
        min-height: 500px;
        animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      ::v-deep .el-badge__content {
        background-color: #5a67d8;
        border: 2px solid white;
        box-shadow: 0 2px 6px rgba(90, 103, 216, 0.3);
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }

      ::v-deep .friend-badge .el-badge__content {
        background-color: #5a67d8;
      }

      ::v-deep .request-badge .el-badge__content {
        background-color: #e53e3e;
        box-shadow: 0 2px 6px rgba(229, 62, 62, 0.3);
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, rgba(226, 232, 240, 0.5), rgba(226, 232, 240, 0));
      }
    }
  }

  .friend-list-container {
    position: relative;
  }

  .friend-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);

    .friend-filters {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      flex: 1;

      .search-filter {
        width: 240px;

        .friend-search-input {
          ::v-deep .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;

            &:focus {
              border-color: #5a67d8;
              box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
            }
          }
        }
      }

      .group-select, .sort-select {
        width: 140px;

        ::v-deep .el-input__inner {
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          transition: all 0.3s ease;

          &:focus {
            border-color: #5a67d8;
            box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
          }
        }
      }
    }

    .view-options {
      .view-mode-switch {
        ::v-deep .el-radio-button__inner {
          border-color: #e2e8f0;
          background: white;
          color: #718096;
          transition: all 0.3s ease;

          &:hover:not(.is-active) {
            color: #5a67d8;
          }
        }

        ::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          background: #5a67d8;
          border-color: #5a67d8;
          box-shadow: -1px 0 0 0 #5a67d8;
          color: white;
        }
      }
    }
  }

  .friend-grid {
    display: grid;
    gap: 24px;
    transition: all 0.3s ease;

    &.grid-view {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }

    &.list-view {
      grid-template-columns: 1fr;
    }
  }

  .no-search-results {
    text-align: center;
    padding: 60px 20px;
    animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    .no-results-icon {
      margin-bottom: 20px;

      i {
        font-size: 70px;
        background: linear-gradient(135deg, #5a67d8, #805ad5);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        opacity: 0.7;
      }
    }

    .no-results-title {
      font-size: 22px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 10px 0;
    }

    .no-results-description {
      font-size: 16px;
      color: #718096;
      margin-bottom: 24px;
    }

    .reset-filters-btn {
      background: linear-gradient(90deg, #5a67d8, #805ad5);
      border: none;
      border-radius: 10px;
      padding: 10px 20px;
      box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);
      transition: all 0.3s ease;

      i {
        margin-right: 6px;
        transition: transform 0.3s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);

        i {
          transform: rotate(180deg);
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
      }
    }
  }

    .empty-friends {
    text-align: center;
    padding: 40px 20px;
    color: #4a5568;
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-width: 800px;
    margin: 0 auto;

    .empty-friends-illustration {
      position: relative;
      margin-bottom: 24px;
      display: inline-block;

      .empty-image {
        width: 120px;
        height: 120px;
        filter: drop-shadow(0 4px 6px rgba(90, 103, 216, 0.2));
        animation: float 3s ease-in-out infinite;
        opacity: 0.8;
      }

      .empty-decoration {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 160px;
        height: 160px;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(90, 103, 216, 0.1) 0%, rgba(90, 103, 216, 0) 70%);
        z-index: -1;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 200px;
          border-radius: 50%;
          border: 2px dashed rgba(90, 103, 216, 0.2);
          animation: rotate 20s linear infinite;
        }
      }
    }

    .empty-title {
      font-size: 24px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 12px;
      position: relative;
      display: inline-block;

      &::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 3px;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        border-radius: 3px;
      }
    }

    .empty-description {
      font-size: 16px;
      color: #718096;
      margin: 16px auto;
      max-width: 400px;
      line-height: 1.6;
    }

    .empty-actions {
      margin: 24px 0 32px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .add-friend-action {
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        border: none;
        box-shadow: 0 4px 12px rgba(90, 103, 216, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(90, 103, 216, 0.4);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(90, 103, 216, 0.3);
        }

        i {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .check-requests-action {
        color: #5a67d8;
        font-size: 15px;
        font-weight: 500;
        position: relative;
        padding: 8px 16px;
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(90, 103, 216, 0.08);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          margin-right: 6px;
          font-size: 15px;
        }

        .request-count-badge {
          position: absolute;
          top: -5px;
          right: -5px;
          background: #e53e3e;
          color: white;
          font-size: 12px;
          font-weight: 600;
          min-width: 18px;
          height: 18px;
          border-radius: 9px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 5px;
          box-shadow: 0 2px 4px rgba(229, 62, 62, 0.3);
        }
      }

      .refresh-action {
        color: #909399;
        font-size: 14px;
        font-weight: 500;
        padding: 6px 12px;
        border-radius: 16px;
        transition: all 0.3s ease;

        &:hover {
          color: #5a67d8;
          background: rgba(90, 103, 216, 0.08);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          margin-right: 4px;
          font-size: 14px;
          transition: transform 0.3s ease;
        }

        &:hover i {
          transform: rotate(180deg);
        }
      }
    }

    .empty-features {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(226, 232, 240, 0.8);

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 16px;
        border-radius: 12px;
        transition: all 0.3s ease;
        text-align: left;

        &:hover {
          background: rgba(247, 250, 252, 0.8);
          transform: translateY(-4px);
        }

        .feature-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: linear-gradient(135deg, rgba(90, 103, 216, 0.1), rgba(128, 90, 213, 0.1));
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          i {
            font-size: 24px;
            color: #5a67d8;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .feature-text {
          h4 {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 6px;
          }

          p {
        font-size: 14px;
            color: #718096;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .empty-requests {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
    animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    .empty-icon {
      margin-bottom: 20px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, rgba(90, 103, 216, 0), rgba(90, 103, 216, 0.3), rgba(90, 103, 216, 0));
        border-radius: 3px;
      }

      i {
        font-size: 70px;
        color: #e6f7ff;
        background: linear-gradient(135deg, #5a67d8, #805ad5);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        opacity: 0.7;
        animation: float 3s ease-in-out infinite;
      }
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 10px 0;
    }

    .empty-description {
      font-size: 15px;
      color: #718096;
      margin: 0 0 24px 0;
      line-height: 1.5;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    .empty-action {
      border-radius: 10px;
      padding: 10px 20px;
      font-weight: 500;
      background: linear-gradient(90deg, #5a67d8, #805ad5);
      border: none;
      box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
      }

      i {
        margin-right: 6px;
        transition: transform 0.3s ease;
      }

      &:hover i {
        transform: rotate(180deg);
      }
    }
  }

  .friend-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 40px rgba(102, 126, 234, 0.15);
      border-color: rgba(102, 126, 234, 0.2);
    }

    &.list-mode {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-right: 20px;

      &:hover {
        transform: translateY(-4px);
      }

      .friend-card-header {
        width: 6px;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;

        .friend-status-indicator {
          top: 20px;
          right: auto;
          left: 50%;
          transform: translateX(-50%);
        }

        .friend-level-badge {
          top: auto;
          left: auto;
          right: -60px;
          bottom: 20px;
        }
      }

      .friend-card-content {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 16px 16px 16px 20px;
        margin-left: 6px;

        .friend-avatar-section {
          margin-right: 20px;
        }

        .friend-info {
          flex: 1;
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 20px;

          .friend-header {
            width: 180px;
          }

          .friend-meta {
            flex: 1;
            flex-direction: row;
            justify-content: flex-start;
            padding: 0;
            background: none;
            border: none;
          }

          .friend-remark {
            width: 200px;
            margin-top: 0;
          }
        }
      }

      .friend-card-footer {
        border-top: none;
        padding: 0 0 0 20px;
        background: none;

        .friend-actions {
          flex-direction: row;
        }
      }
    }

    .friend-card-header {
      position: relative;
      height: 4px;
      background: linear-gradient(90deg, #5a67d8 0%, #805ad5 100%);

      .friend-status-indicator {
        position: absolute;
        top: 0;
        right: 16px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        transform: translateY(-50%);
        border: 2px solid white;
        transition: all 0.3s ease;

        &.online {
          background: #48bb78;
          box-shadow: 0 0 10px rgba(72, 187, 120, 0.7);
        }

        &.recent {
          background: #ed8936;
          box-shadow: 0 0 8px rgba(237, 137, 54, 0.5);
        }

        &.week {
          background: #4299e1;
          box-shadow: 0 0 8px rgba(66, 153, 225, 0.5);
        }

        &.inactive {
          background: #a0aec0;
        }
      }

      .friend-level-badge {
        position: absolute;
        top: -4px;
        left: 16px;
        background: linear-gradient(90deg, #f6ad55, #ed8936);
        color: white;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
        box-shadow: 0 4px 12px rgba(237, 137, 54, 0.25);
        transition: all 0.3s ease;

        i {
          font-size: 10px;
        }
      }
    }

    .friend-card-content {
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .friend-avatar-section {
      text-align: center;

      .avatar-container {
        position: relative;
        display: inline-block;
        margin-bottom: 8px;

        .friend-avatar {
          border: 3px solid #f8f9fa;
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
          transition: all 0.3s ease;
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .avatar-ring {
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          border: 2px solid transparent;
          border-radius: 50%;
          transition: all 0.3s ease;

          &.active {
            border-color: #52c41a;
            animation: pulse 2s infinite;
          }
        }
      }

      .online-status {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
        background: rgba(248, 249, 250, 0.8);
        border-radius: 12px;
        padding: 2px 8px;

        &.online {
          color: #52c41a;
          background: rgba(82, 196, 26, 0.1);

          &::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #52c41a;
            border-radius: 50%;
            animation: blink 1.5s infinite;
          }
        }

        &:not(.online) {
          color: #8c8c8c;

          &::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #d9d9d9;
            border-radius: 50%;
          }
        }

        .status-text {
          font-size: 11px;
          font-weight: 600;
        }
      }
    }

    .friend-info {
      flex: 1;
      min-width: 0; // 防止flex子项溢出

      .friend-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .friend-name {
          font-size: 18px;
          font-weight: 700;
          color: #2c3e50;
          margin: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .friend-badges {
          .vip-badge {
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 2px;
            box-shadow: 0 2px 6px rgba(238, 90, 36, 0.3);

            i {
              font-size: 9px;
            }
          }
        }
      }

      .friend-meta {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin-bottom: 10px;
        background: rgba(247, 250, 252, 0.7);
        padding: 8px 12px;
        border-radius: 10px;
        border: 1px solid rgba(226, 232, 240, 0.8);

        .meta-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;

          i {
            width: 16px;
            font-size: 14px;
            color: #5a67d8;
            opacity: 0.8;
          }

          .meta-label {
            color: #718096;
            min-width: 32px;
            font-weight: 500;
          }

          .meta-value {
            color: #2d3748;
            font-weight: 600;
            margin-left: auto;
          }
        }
      }

      .friend-remark {
        background: rgba(237, 242, 247, 0.7);
        padding: 8px 12px;
        border-radius: 10px;
        font-size: 13px;
        color: #5a67d8;
        display: flex;
        align-items: center;
        gap: 8px;
        border-left: 3px solid #5a67d8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);

        i {
          font-size: 14px;
          flex-shrink: 0;
        }

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #4a5568;
        }
      }
    }

    .friend-card-footer {
      padding: 16px 20px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      background: rgba(248, 249, 250, 0.5);

      .friend-actions {
        display: flex;
        gap: 10px;

        .action-btn {
          flex: 1;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
          }

          &:hover::before {
            left: 100%;
          }

          &.primary-btn {
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            border: none;
            color: white;
            box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
            }

            i {
              margin-right: 6px;
              font-size: 14px;
            }
          }

          &.more-btn {
            color: #718096;
            border: 1px solid #e2e8f0;
            background: white;
            width: 36px;
            flex: none;

            &:hover {
              color: #5a67d8;
              border-color: #5a67d8;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }

        .more-dropdown {
          ::v-deep .friend-dropdown {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(226, 232, 240, 0.6);
            padding: 6px 0;
            overflow: hidden;

            .el-dropdown-menu__item {
              padding: 10px 16px;
              font-size: 14px;
              transition: all 0.2s ease;
              color: #4a5568;

              &:hover {
                background: rgba(90, 103, 216, 0.06);
                color: #5a67d8;
              }

              &:active {
                background: rgba(90, 103, 216, 0.1);
              }

              i {
                margin-right: 8px;
                width: 16px;
                font-size: 14px;
              }

              &[divided] {
                border-top: 1px solid rgba(226, 232, 240, 0.6);
                margin-top: 4px;
                padding-top: 10px;
                color: #e53e3e;

                &:hover {
                  background: rgba(229, 62, 62, 0.06);
                  color: #e53e3e;
                }
              }
            }
          }
        }
      }
    }
  }

  // 动画定义
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(90, 103, 216, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(90, 103, 216, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(90, 103, 216, 0);
    }
  }

  @keyframes pulseGreen {
    0% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(72, 187, 120, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
    }
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0.3;
    }
  }

  @keyframes loading-bounce {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes heartbeat {
    0% {
      transform: scale(1);
    }
    14% {
      transform: scale(1.1);
    }
    28% {
      transform: scale(1);
    }
    42% {
      transform: scale(1.1);
    }
    70% {
      transform: scale(1);
    }
  }

  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  @keyframes rotate {
    0% {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @keyframes shine {
    0% {
      background-position: -100% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }

  // 页面进入动画
  .friend-list {
    animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .friend-grid {
    perspective: 1000px;
  }

  .friend-card {
    animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation-fill-mode: both;
    transform-origin: center bottom;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.05 + 0.1}s;
      }
    }

    &:hover {
      transform: translateY(-8px) scale(1.01);
      box-shadow: 0 16px 30px rgba(90, 103, 216, 0.12);

      .friend-avatar {
        transform: scale(1.05);
      }

      .friend-status-indicator {
        transform: translateY(-50%) scale(1.2);
      }
    }

    &:active {
      transform: translateY(-4px) scale(0.99);
    }
  }

  .user-card {
    animation: slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation-fill-mode: both;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.03 + 0.1}s;
      }
    }

    &:hover {
      transform: translateY(-8px);

      .user-avatar {
        transform: scale(1.05);
      }
    }
  }

  .request-card {
    animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation-fill-mode: both;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.05 + 0.1}s;
      }
    }

    &:hover {
      transform: translateY(-4px);

      .request-badge {
        animation: pulse 1.5s infinite;
      }
    }
  }

  // 特殊状态动画
  .friend-card:hover .friend-level-badge {
    animation: float 2s ease-in-out infinite;
  }

  .friend-card:hover .online-status.online::before {
    animation: pulseGreen 1.5s infinite;
  }

  .user-card.high-score:hover::before {
    animation: shine 2s linear infinite;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    background-size: 200% 100%;
  }

  // 按钮交互动效
  .action-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%);
      transform-origin: 50% 50%;
    }

    &:focus:not(:active)::after {
      animation: ripple 0.6s ease-out;
    }

    &:active {
      transform: scale(0.96);
    }
  }

  .add-friend-btn:active {
    transform: scale(0.96);
  }

  // 搜索输入框交互动效
  .search-input {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }

    ::v-deep .el-input__inner {
      transition: all 0.3s ease;

      &:focus {
        transform: scale(1.01);
        box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
      }
    }
  }

  // 标签页切换动画
  .el-tabs__content {
    position: relative;

    .el-tab-pane {
      animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
  }

  // 徽章动画
  .el-badge__content {
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  // 统计数字动画
  .stat-number {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, rgba(90, 103, 216, 0), rgba(90, 103, 216, 0.5), rgba(90, 103, 216, 0));
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }
  }

  .stat-item:hover .stat-number::after {
    transform: scaleX(1);
  }

  // 头像动画
  .friend-avatar, .user-avatar, .request-avatar {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  // 好友状态指示器动画
  .friend-status-indicator {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.online {
      animation: pulseGreen 2s infinite;
    }
  }

  .add-friend-section {
    .search-container {
      margin-bottom: 40px;
      position: relative;

      .search-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 28px;
        position: relative;

        .search-title-section {
          position: relative;

          .search-title {
            font-size: 26px;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;

            i {
              color: #5a67d8;
              font-size: 22px;
              background: linear-gradient(90deg, #5a67d8, #805ad5);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }

            &::after {
              content: '';
              position: absolute;
              bottom: -4px;
              left: 0;
              width: 40px;
              height: 3px;
              background: linear-gradient(90deg, #5a67d8, #805ad5);
              border-radius: 3px;
            }
          }

          .search-description {
            font-size: 15px;
            color: #718096;
            margin: 0;
            padding-top: 6px;
          }
        }

        .search-stats {
          .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.7);
            padding: 8px 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            }

            .stat-number {
              font-size: 22px;
              font-weight: 700;
              background: linear-gradient(90deg, #5a67d8, #805ad5);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              line-height: 1.2;
            }

            .stat-label {
              font-size: 12px;
              color: #718096;
              margin-top: 2px;
              font-weight: 500;
            }
          }
        }
      }

      .search-box {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -10px;
          left: 20px;
          right: 20px;
          height: 1px;
          background: linear-gradient(90deg,
            rgba(226, 232, 240, 0),
            rgba(226, 232, 240, 0.8),
            rgba(226, 232, 240, 0)
          );
        }

        .search-input-wrapper {
          display: flex;
          gap: 12px;
          margin-bottom: 16px;
          position: relative;

          .search-input {
            flex: 1;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border-radius: 12px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
              pointer-events: none;
              z-index: 0;
            }

            ::v-deep .el-input__inner {
              border-radius: 12px;
              border: 1px solid #e2e8f0;
              padding-left: 50px;
              height: 48px;
              font-size: 15px;
              transition: all 0.3s ease;
              background: rgba(255, 255, 255, 0.95);
              color: #4a5568;

              &:focus {
                border-color: #5a67d8;
                box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.15);
                background: white;
              }

              &::placeholder {
                color: #a0aec0;
                font-weight: 400;
              }
            }

            .search-icon {
							position: absolute;
							top: 14px;
              color: #a0aec0;
              font-size: 18px;
              transition: all 0.3s ease;
            }

            &:focus-within .search-icon {
              color: #5a67d8;
            }
          }

          .search-btn {
            border-radius: 12px;
            padding: 0 24px;
            font-weight: 600;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            border: none;
            transition: all 0.3s ease;
            height: 48px;
            box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
            }

            i {
              margin-right: 6px;
              font-size: 16px;
            }
          }
        }

        .search-suggestions {
          display: flex;
          gap: 10px;
          justify-content: flex-start;
          margin-left: 4px;

          .suggestion-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 14px;
            background: rgba(90, 103, 216, 0.08);
            border-radius: 20px;
            color: #5a67d8;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(90, 103, 216, 0.1);

            &:hover {
              background: rgba(90, 103, 216, 0.12);
              transform: translateY(-2px) scale(1.02);
              box-shadow: 0 4px 8px rgba(90, 103, 216, 0.1);
            }

            &:active {
              transform: translateY(0) scale(0.98);
            }

            i {
              font-size: 14px;
            }
          }
        }
      }
    }

    .search-loading {
      text-align: center;
      padding: 60px 20px;
      position: relative;

      .loading-animation {
        margin-bottom: 20px;
        position: relative;

        .loading-dots {
          display: flex;
          justify-content: center;
          gap: 8px;
          position: relative;

          span {
            width: 10px;
            height: 10px;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            border-radius: 50%;
            animation: loading-bounce 1.4s infinite ease-in-out both;
            box-shadow: 0 2px 6px rgba(90, 103, 216, 0.3);

            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
            &:nth-child(3) { animation-delay: 0s; }
          }

          &::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg,
              rgba(90, 103, 216, 0),
              rgba(90, 103, 216, 0.3),
              rgba(90, 103, 216, 0)
            );
            border-radius: 2px;
          }
        }
      }

      .loading-text {
        font-size: 15px;
        color: #718096;
        margin: 0;
        font-weight: 500;
      }
    }

    .search-results {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: -20px;
        left: 20px;
        right: 20px;
        height: 1px;
        background: linear-gradient(90deg,
          rgba(226, 232, 240, 0),
          rgba(226, 232, 240, 0.8),
          rgba(226, 232, 240, 0)
        );
      }

      .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(226, 232, 240, 0.8);
        position: relative;

        .results-title {
          font-size: 20px;
          font-weight: 600;
          color: #2d3748;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 10px;

          i {
            color: #5a67d8;
            font-size: 18px;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .results-count {
            color: #718096;
            font-weight: 500;
            font-size: 15px;
          }
        }

        .results-actions {
          .clear-btn {
            color: #718096;
            font-weight: 500;
            transition: all 0.3s ease;

            i {
              margin-right: 4px;
              transition: transform 0.3s ease;
            }

            &:hover {
              color: #5a67d8;

              i {
                transform: rotate(-45deg);
              }
            }
          }
        }
      }

      .results-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
      }
    }

    .no-results {
      text-align: center;
      padding: 60px 20px;
      animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      .no-results-icon {
        margin-bottom: 20px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, rgba(90, 103, 216, 0), rgba(90, 103, 216, 0.3), rgba(90, 103, 216, 0));
          border-radius: 3px;
        }

        i {
          font-size: 70px;
          color: #e6f7ff;
          background: linear-gradient(135deg, #5a67d8, #805ad5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          opacity: 0.7;
        }
      }

      .no-results-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 10px 0;
      }

      .no-results-description {
        font-size: 15px;
        color: #718096;
        margin: 0 0 24px 0;

        strong {
          color: #5a67d8;
          font-weight: 600;
        }
      }

      .no-results-suggestions {
        background: rgba(247, 250, 252, 0.8);
        padding: 16px 20px;
        border-radius: 12px;
        margin: 20px auto;
        text-align: left;
        max-width: 400px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);

        .suggestions-title {
          font-size: 14px;
          font-weight: 600;
          color: #2d3748;
          margin: 0 0 10px 0;
          display: flex;
          align-items: center;
          gap: 6px;

          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 14px;
            background: linear-gradient(to bottom, #5a67d8, #805ad5);
            border-radius: 2px;
          }
        }

        .suggestions-list {
          margin: 0;
          padding-left: 20px;
          color: #718096;

          li {
            margin: 6px 0;
            font-size: 14px;
            position: relative;

            &::marker {
              color: #5a67d8;
            }
          }
        }
      }

      .retry-btn {
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 500;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        border: none;
        box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
        }

        i {
          margin-right: 6px;
          transition: transform 0.3s ease;
        }

        &:hover i {
          transform: rotate(180deg);
        }
      }
    }

    .search-placeholder {
      text-align: center;
      padding: 60px 20px;
      animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      .placeholder-content {
        max-width: 500px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.6);
        padding: 30px;
        border-radius: 16px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(226, 232, 240, 0.8);

        .placeholder-icon {
          margin-bottom: 24px;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, rgba(90, 103, 216, 0), rgba(90, 103, 216, 0.3), rgba(90, 103, 216, 0));
            border-radius: 3px;
          }

          i {
            font-size: 80px;
            color: #e6f7ff;
            background: linear-gradient(135deg, #5a67d8, #805ad5);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: float 3s ease-in-out infinite;
          }
        }

        .placeholder-title {
          font-size: 22px;
          font-weight: 700;
          color: #2d3748;
          margin: 0 0 12px 0;
        }

        .placeholder-description {
          font-size: 15px;
          color: #718096;
          margin: 0 0 30px 0;
          line-height: 1.6;
        }

        .placeholder-features {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;

          .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            color: #5a67d8;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-4px);
            }

            i {
              font-size: 20px;
              padding: 14px;
              background: rgba(90, 103, 216, 0.08);
              border-radius: 50%;
              transition: all 0.3s ease;
              box-shadow: 0 4px 12px rgba(90, 103, 216, 0.1);
            }

            &:hover i {
              background: rgba(90, 103, 216, 0.12);
              transform: scale(1.1);
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }

    .user-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 1px solid rgba(0, 0, 0, 0.04);
      position: relative;

      &:hover {
        transform: translateY(-6px);
        box-shadow: 0 12px 30px rgba(90, 103, 216, 0.12);
        border-color: rgba(90, 103, 216, 0.1);
      }

      &.is-friend {
        border-color: #48bb78;
        background: linear-gradient(135deg, rgba(72, 187, 120, 0.05), rgba(255, 255, 255, 0.9));

        &::before {
          background: linear-gradient(90deg, #48bb78, #68d391);
        }

        .user-status-badge {
          background: #48bb78;
          box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
        }
      }

      &.request-sent {
        border-color: #ed8936;
        background: linear-gradient(135deg, rgba(237, 137, 54, 0.05), rgba(255, 255, 255, 0.9));

        &::before {
          background: linear-gradient(90deg, #ed8936, #f6ad55);
        }

        .user-status-badge {
          background: #ed8936;
          box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
        }
      }

      &.high-score {
        &::before {
          background: linear-gradient(90deg, #e53e3e, #fc8181);
        }
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
      }

      .user-card-header {
        padding: 14px 16px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .user-level-indicator {
          background: linear-gradient(90deg, #f6ad55, #ed8936);
          color: white;
          padding: 3px 8px;
          border-radius: 20px;
          font-size: 11px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 4px;
          box-shadow: 0 2px 8px rgba(237, 137, 54, 0.25);
          transition: all 0.3s ease;

          i {
            font-size: 10px;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
          }
        }

        .user-status-badge {
          padding: 3px 8px;
          border-radius: 20px;
          font-size: 11px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 4px;
          background: #5a67d8;
          color: white;
          box-shadow: 0 2px 8px rgba(90, 103, 216, 0.2);
          transition: all 0.3s ease;

          &.pending {
            background: #ed8936;
          }

          i {
            font-size: 10px;
          }

          &:hover {
            transform: translateY(-2px);
          }
        }
      }

      .user-card-content {
        padding: 16px;
        text-align: center;

        .user-avatar-section {
          position: relative;
          display: inline-block;
          margin-bottom: 14px;

          .user-avatar {
            border: 2px solid #f0f0f0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }

          .avatar-decoration {
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border: 2px solid transparent;
            border-radius: 50%;
            background: linear-gradient(45deg, #5a67d8, #805ad5);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            transform: scale(1.1);
          }

          &:hover .avatar-decoration {
            opacity: 0.7;
            transform: scale(1);
          }
        }

        .user-info {
          .user-name {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 10px 0;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;

            &::after {
              content: '';
              position: absolute;
              bottom: -4px;
              left: 50%;
              width: 0;
              height: 2px;
              background: linear-gradient(90deg, #5a67d8, #805ad5);
              transition: all 0.3s ease;
              transform: translateX(-50%);
              border-radius: 2px;
              opacity: 0;
            }
          }

          &:hover .user-name::after {
            width: 70%;
            opacity: 1;
          }

          .user-meta {
            display: flex;
            flex-direction: column;
            gap: 6px;
            background: rgba(247, 250, 252, 0.7);
            padding: 8px 10px;
            border-radius: 10px;
            border: 1px solid rgba(226, 232, 240, 0.6);
            margin-top: 8px;

            .meta-item {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 6px;
              font-size: 13px;
              color: #718096;
              transition: all 0.3s ease;

              &:hover {
                transform: translateX(2px);
              }

              i {
                color: #5a67d8;
                font-size: 13px;
                opacity: 0.8;
              }

              &:first-child {
                color: #48bb78;
                font-weight: 600;

                i {
                  color: #48bb78;
                }
              }
            }
          }
        }
      }

      .user-card-footer {
        padding: 14px 16px;
        border-top: 1px solid rgba(226, 232, 240, 0.6);
        background: rgba(247, 250, 252, 0.5);

        .add-friend-btn {
          width: 100%;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          position: relative;
          overflow: hidden;
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
          }

          &:hover::before {
            left: 100%;
          }

          &:not([disabled]):hover {
            transform: translateY(-2px);
          }

          &:active:not([disabled]) {
            transform: translateY(0) scale(0.98);
          }

          &[disabled] {
            opacity: 0.7;
            cursor: not-allowed;
          }

          &.el-button--primary {
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            border: none;
            box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);

            &:hover:not([disabled]) {
              box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
            }

            i {
              margin-right: 6px;
              font-size: 14px;
              transition: transform 0.3s ease;
            }

            &:hover i {
              transform: rotate(360deg);
            }
          }

          &.el-button--success {
            background: linear-gradient(90deg, #48bb78, #68d391);
            border: none;
            box-shadow: 0 4px 12px rgba(72, 187, 120, 0.2);

            &:hover:not([disabled]) {
              box-shadow: 0 6px 16px rgba(72, 187, 120, 0.3);
            }
          }

          &.el-button--info {
            background: linear-gradient(90deg, #a0aec0, #cbd5e0);
            border: none;
            box-shadow: 0 4px 12px rgba(160, 174, 192, 0.2);

            &:hover:not([disabled]) {
              box-shadow: 0 6px 16px rgba(160, 174, 192, 0.3);
            }
          }
        }
      }
    }
  }

  // 好友请求区域样式
  .request-section {
    .loading-container {
      text-align: center;
      padding: 60px 20px;
      color: #718096;
      animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      p {
        margin-top: 16px;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .empty-requests {
      text-align: center;
      padding: 60px 20px;
      animation: fadeInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      .empty-icon {
        margin-bottom: 20px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, rgba(90, 103, 216, 0), rgba(90, 103, 216, 0.3), rgba(90, 103, 216, 0));
          border-radius: 3px;
        }

        i {
          font-size: 70px;
          color: #e6f7ff;
          background: linear-gradient(135deg, #5a67d8, #805ad5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          opacity: 0.7;
          animation: float 3s ease-in-out infinite;
        }
      }

      .empty-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 10px 0;
      }

      .empty-description {
        font-size: 15px;
        color: #718096;
        margin: 0 0 24px 0;
        line-height: 1.5;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
      }

      .empty-action {
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 500;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        border: none;
        box-shadow: 0 4px 12px rgba(90, 103, 216, 0.2);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(90, 103, 216, 0.3);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(90, 103, 216, 0.25);
        }

        i {
          margin-right: 6px;
          transition: transform 0.3s ease;
        }

        &:hover i {
          transform: rotate(180deg);
        }
      }
    }

    .request-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid rgba(226, 232, 240, 0.8);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #5a67d8, #805ad5);
        border-radius: 0 0 3px 3px;
      }

      .request-title {
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;

        i {
          color: #5a67d8;
          font-size: 18px;
          background: linear-gradient(90deg, #5a67d8, #805ad5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .request-count {
          color: #718096;
          font-weight: 500;
          font-size: 15px;
        }
      }

      .request-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .refresh-action {
          color: #909399;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 6px 12px;
          border-radius: 20px;

          &:hover {
            color: #5a67d8;
            background: rgba(90, 103, 216, 0.08);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }

          i {
            transition: transform 0.3s ease;
          }

          &:hover i {
            transform: rotate(180deg);
          }
        }

        .batch-action {
          color: #5a67d8;
          font-weight: 500;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          border-radius: 20px;
          background: rgba(90, 103, 216, 0.08);
          border: 1px solid rgba(90, 103, 216, 0.1);

          &:hover {
            color: #4c51bf;
            background: rgba(90, 103, 216, 0.12);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(90, 103, 216, 0.1);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .request-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 20px;
    }

    .request-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 1px solid rgba(0, 0, 0, 0.04);
      position: relative;

      &:hover {
        transform: translateY(-6px);
        box-shadow: 0 12px 30px rgba(90, 103, 216, 0.12);
        border-color: rgba(90, 103, 216, 0.1);
      }

      &.processing {
        opacity: 0.7;
        pointer-events: none;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #48bb78, #68d391);
      }

      .request-card-header {
        padding: 18px 18px 14px;
        display: flex;
        align-items: center;
        gap: 14px;
      }

      .request-avatar-section {
        position: relative;

        .request-avatar {
          border: 2px solid #f0f0f0;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .request-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          width: 18px;
          height: 18px;
          background: #48bb78;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid white;
          box-shadow: 0 2px 6px rgba(72, 187, 120, 0.3);
          transition: all 0.3s ease;

          i {
            color: white;
            font-size: 10px;
          }
        }

        &:hover {
          .request-avatar {
            transform: scale(1.05);
          }

          .request-badge {
            transform: scale(1.2);
          }
        }
      }

      .request-info {
        flex: 1;
        min-width: 0;

        .request-name {
          font-size: 16px;
          font-weight: 600;
          color: #2d3748;
          margin: 0 0 6px 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: all 0.3s ease;
          position: relative;
          display: inline-block;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #5a67d8, #805ad5);
            transition: all 0.3s ease;
            border-radius: 2px;
            opacity: 0;
          }

          &:hover::after {
            width: 100%;
            opacity: 1;
          }
        }

        .request-time, .request-email {
          font-size: 13px;
          margin: 4px 0;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.3s ease;

          i {
            font-size: 13px;
            transition: all 0.3s ease;
          }

          &:hover {
            transform: translateX(2px);

            i {
              transform: scale(1.1);
            }
          }
        }

        .request-time {
          color: #718096;

          i {
            color: #a0aec0;
          }
        }

        .request-email {
          color: #5a67d8;

          i {
            color: #5a67d8;
          }
        }
      }

      .request-message {
        padding: 0 18px 14px;

        .message-content {
          background: rgba(247, 250, 252, 0.7);
          padding: 10px 14px;
          border-radius: 10px;
          font-size: 13px;
          color: #4a5568;
          display: flex;
          align-items: flex-start;
          gap: 8px;
          line-height: 1.5;
          border: 1px solid rgba(226, 232, 240, 0.6);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(237, 242, 247, 0.9);
            border-color: rgba(90, 103, 216, 0.2);
            transform: translateY(-2px);
          }

          i {
            margin-top: 2px;
            flex-shrink: 0;
            color: #5a67d8;
            opacity: 0.8;
          }
        }
      }

      .request-actions {
        padding: 14px 18px 18px;
        display: flex;
        gap: 10px;

        .accept-btn, .reject-btn {
          flex: 1;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
          }

          &:hover::before {
            left: 100%;
          }

          &:hover {
            transform: translateY(-2px);
          }

          &:active {
            transform: translateY(0) scale(0.98);
          }

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }

        .accept-btn {
          background: linear-gradient(90deg, #48bb78, #68d391);
          border: none;
          box-shadow: 0 4px 12px rgba(72, 187, 120, 0.2);

          &:hover {
            box-shadow: 0 6px 16px rgba(72, 187, 120, 0.3);
          }

          &:active {
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.25);
          }

          i {
            transition: transform 0.3s ease;
          }

          &:hover i {
            transform: scale(1.2);
          }
        }

        .reject-btn {
          background: white;
          border: 1px solid #e53e3e;
          color: #e53e3e;

          &:hover {
            background: rgba(229, 62, 62, 0.04);
            box-shadow: 0 6px 16px rgba(229, 62, 62, 0.15);
          }

          &:active {
            box-shadow: 0 2px 8px rgba(229, 62, 62, 0.1);
          }

          i {
            transition: transform 0.3s ease;
          }

          &:hover i {
            transform: rotate(90deg);
          }
        }
      }
    }
  }

  .friend-badge, .request-badge {
    margin-left: 8px;
  }

  .add-friend-section {
    .search-results {
      margin-top: 20px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #909399;

    i {
      font-size: 64px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      font-size: 16px;
      margin: 0;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .tabs-container {
      padding: 30px 24px 40px;
    }
  }

  @media (max-width: 1024px) {
    .tabs-container {
      padding: 24px 20px 32px;
    }

    .friend-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }

    .friend-card {
      .friend-card-content {
        padding: 18px 16px;
        gap: 12px;

        .friend-avatar-section {
          .avatar-container .friend-avatar {
            width: 56px !important;
            height: 56px !important;
          }
        }

        .friend-info {
          .friend-meta {
            padding: 6px 10px;

            .meta-item {
              font-size: 12px;
            }
          }
        }
      }
    }

    .add-friend-section {
      .search-container .search-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .search-stats {
          align-self: flex-end;
        }
      }

      .results-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
      }
    }

    .request-section .request-grid {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      padding: 24px 16px;

      .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
      }

      .title-section .page-title {
        font-size: 26px;
        justify-content: center;

        i {
          font-size: 22px;
        }
      }

      .header-stats {
        gap: 20px;
        justify-content: center;

        .stat-item {
          padding: 8px 16px;

          .stat-number {
            font-size: 22px;
          }
        }
      }
    }

    .tabs-container {
      padding: 20px 16px 30px;

      .custom-tabs {
        border-radius: 12px;

        ::v-deep .el-tabs__header {
          .el-tabs__nav-wrap {
            padding: 0 12px;
          }

          .el-tabs__nav {
            padding: 4px 0;
          }

          .el-tabs__item {
            padding: 12px 16px;
            font-size: 14px;
            margin: 0 2px;

            .tab-label {
              gap: 6px;

              i {
                font-size: 16px;
              }
            }
          }
        }

        ::v-deep .el-tabs__content {
          padding: 20px 16px;
        }
      }
    }

    .friend-grid {
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      gap: 16px;
    }

    .friend-card {
      .friend-card-content {
        padding: 16px;
        gap: 12px;

      .friend-avatar-section {
        .avatar-container .friend-avatar {
            width: 52px !important;
            height: 52px !important;
          }
        }
      }

      .friend-info {
        .friend-header .friend-name {
          font-size: 16px;
        }

        .friend-meta {
          padding: 6px 10px;

          .meta-item {
            font-size: 12px;
            gap: 6px;

            i {
          font-size: 13px;
            }
          }
        }
      }

      .friend-card-footer {
        padding: 12px 16px;

        .friend-actions {
          gap: 8px;

          .action-btn {
            font-size: 13px;
            height: 34px;

            &.more-btn {
              width: 34px;
            }
          }
        }
      }
    }

    .add-friend-section {
      .search-container {
        margin-bottom: 28px;

        .search-header {
          margin-bottom: 20px;

          .search-title-section .search-title {
            font-size: 22px;

            i {
              font-size: 20px;
            }
          }
        }

        .search-box {
          .search-input-wrapper {
            flex-direction: column;
            gap: 12px;

            .search-btn {
              width: 100%;
              height: 46px;
            }

            .search-input {
              ::v-deep .el-input__inner {
                height: 46px;
              }
            }
          }

          .search-suggestions {
            flex-direction: row;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-top: 16px;

            .suggestion-item {
              justify-content: center;
            }
          }
        }
      }

      .results-grid, .request-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 16px;
      }

      .user-card {
        .user-card-content {
          padding: 16px;

          .user-avatar-section {
            margin-bottom: 12px;

            .user-avatar {
              width: 52px !important;
              height: 52px !important;
            }
          }

          .user-info .user-name {
            font-size: 16px;
          }
        }

        .user-card-footer {
          padding: 12px 16px;
        }
      }

      .search-placeholder .placeholder-content {
        padding: 50px 20px;

        .placeholder-icon i {
          font-size: 70px;
        }

        .placeholder-title {
          font-size: 20px;
        }

        .placeholder-features {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;
        }
      }
    }

    .request-section {
      .request-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .request-title {
          font-size: 18px;
        }
      }

      .request-card {
        .request-card-header {
          padding: 16px 16px 12px;
          gap: 12px;

          .request-avatar-section .request-avatar {
            width: 48px !important;
            height: 48px !important;
          }

          .request-info .request-name {
            font-size: 15px;
          }
        }

        .request-message {
          padding: 0 16px 12px;
        }

        .request-actions {
          padding: 12px 16px;
          gap: 8px;

          .accept-btn, .reject-btn {
            font-size: 13px;
            height: 34px;
          }
        }
      }
    }
  }

  @media (max-width: 576px) {
    .friend-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .add-friend-section {
      .results-grid, .request-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .search-placeholder .placeholder-features {
        grid-template-columns: 1fr;
        gap: 16px;

        .feature-item {
          flex-direction: row;
          gap: 12px;
          justify-content: center;

          i {
            font-size: 20px;
            padding: 10px;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .page-header {
      padding: 16px 12px;

      .title-section .page-title {
        font-size: 22px;
        gap: 8px;

        i {
          font-size: 18px;
        }
      }

      .header-stats {
        gap: 12px;
        flex-direction: row;

        .stat-item {
          padding: 6px 12px;

          .stat-number {
            font-size: 18px;
          }

          .stat-label {
            font-size: 12px;
          }
        }
      }
    }

    .tabs-container {
      padding: 16px 12px 24px;

      .custom-tabs {
        ::v-deep .el-tabs__header {
          .el-tabs__nav-wrap {
            padding: 0 6px;
          }

          .el-tabs__nav {
            padding: 3px 0;
          }

          .el-tabs__item {
            padding: 10px 8px;
            font-size: 13px;
            margin: 0 1px;

            .tab-label {
              gap: 4px;

              i {
                font-size: 14px;
              }
            }
          }
        }

        ::v-deep .el-tabs__content {
          padding: 16px 12px;
        }
      }
    }

    .friend-card {
      .friend-card-content {
        padding: 16px 12px;
        gap: 10px;

        .friend-avatar-section {
          .avatar-container .friend-avatar {
            width: 48px !important;
            height: 48px !important;
          }

          .online-status {
            font-size: 10px;
            padding: 1px 6px;
          }
        }
      }

      .friend-info {
        .friend-header .friend-name {
          font-size: 15px;
        }

        .friend-meta {
          margin-bottom: 8px;
        }

        .friend-remark {
          font-size: 12px;
          padding: 6px 10px;
        }
      }

      .friend-card-footer {
        padding: 10px 12px 12px;

        .friend-actions {
          flex-direction: row;
          gap: 8px;

          .action-btn {
            height: 32px;
            font-size: 12px;

            &.primary-btn {
              i {
                margin-right: 4px;
                font-size: 13px;
              }
            }

            &.more-btn {
              width: 32px;
            }
          }
        }
      }
    }

    .add-friend-section {
      .search-container .search-header .search-title-section {
        .search-title {
        font-size: 20px;
      }

        .search-description {
          font-size: 14px;
        }
      }
    }

    .request-section {
      .request-card {
        .request-actions {
          flex-direction: row;

          .accept-btn, .reject-btn {
            flex: 1;
            font-size: 12px;
            height: 32px;
          }
        }
      }
    }
  }

  // 超小屏幕优化
  @media (max-width: 360px) {
    .page-header {
      .title-section .page-title {
        font-size: 20px;
      }

      .header-stats {
        .stat-item {
          padding: 4px 10px;

          .stat-number {
            font-size: 16px;
          }

          .stat-label {
            font-size: 11px;
          }
        }
      }
    }

    .tabs-container {
      padding: 12px 8px 20px;

      .custom-tabs {
        ::v-deep .el-tabs__header {
          .el-tabs__item {
            padding: 8px 6px;
          font-size: 12px;
          }
        }
      }
    }

    .friend-card, .user-card, .request-card {
      border-radius: 12px;
    }

    .friend-card {
      .friend-card-footer {
        .friend-actions {
          flex-direction: column;

          .action-btn {
            width: 100%;

            &.more-btn {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
